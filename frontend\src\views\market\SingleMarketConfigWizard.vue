<template>
  <div class="market-config-wizard">
    <h2>单个市场配置</h2>

    <el-steps :active="activeStep" finish-status="success" class="config-steps">
      <el-step title="时间尺度选择" description="选择市场时间尺度"></el-step>
      <el-step title="交易品种选择" description="选择交易品种"></el-step>
      <el-step title="交易类型选择" description="选择交易类型"></el-step>
      <el-step title="报价方式选择" description="选择报价方式"></el-step>
      <el-step title="结算方式选择" description="选择结算方式"></el-step>
      <el-step title="市场整体信息配置" description="配置市场基本信息"></el-step>
      <el-step title="市场主体信息配置" description="配置市场主体信息"></el-step>
    </el-steps>

    <div class="step-content">
      <!-- 步骤1: 时间尺度选择 -->
      <div v-if="activeStep === 1" class="step-container">
        <h3>时间尺度选择</h3>
        <el-card class="option-card">
          <el-radio v-model="marketForm.time_scale" label="day_ahead" border>日前</el-radio>
        </el-card>
      </div>

      <!-- 步骤2: 交易品种选择 -->
      <div v-if="activeStep === 2" class="step-container">
        <h3>交易品种选择</h3>
        <el-card class="option-card">
          <el-radio v-model="marketForm.product_type" label="energy" border>调频</el-radio>
        </el-card>
      </div>

      <!-- 步骤3: 交易类型选择 -->
      <div v-if="activeStep === 3" class="step-container">
        <h3>交易类型选择</h3>
        <el-card class="option-card">
          <el-radio v-model="marketForm.trade_type" label="centralized_bidding" border>集中竞价</el-radio>
        </el-card>
      </div>

      <!-- 步骤4: 报价方式选择 -->
      <div v-if="activeStep === 4" class="step-container">
        <h3>报价方式选择</h3>
        <el-card class="option-card">
          <el-radio v-model="marketForm.bidding_method" label="one_side" border>单边报价</el-radio>
        </el-card>
      </div>

      <!-- 步骤5: 结算方式选择 -->
      <div v-if="activeStep === 5" class="step-container">
        <h3>结算方式选择</h3>
        <el-card class="option-card">
          <el-radio v-model="marketForm.settlement_method" label="nodal_marginal_price" border>节点边际电价出清</el-radio>
        </el-card>
      </div>

      <!-- 步骤6: 市场整体信息配置 -->
      <div v-if="activeStep === 6" class="step-container">
        <h3>市场整体信息配置</h3>
        <el-form :model="marketForm" label-width="180px" class="market-form">
          <el-form-item label="市场名称" required>
            <el-input v-model="marketForm.name" placeholder="请输入市场名称"></el-input>
          </el-form-item>

          <el-form-item label="市场描述">
            <el-input v-model="marketForm.description" type="textarea" :rows="3" placeholder="请输入市场描述"></el-input>
          </el-form-item>

          <el-form-item label="开始时间" required>
            <el-date-picker
              v-model="marketForm.start_date"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="结束时间" required>
            <el-date-picker
              v-model="marketForm.end_date"
              type="datetime"
              placeholder="选择结束时间"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="市场最小成交价格">
            <el-input-number v-model="marketForm.min_clearing_price" :min="0" :precision="2" :step="0.01" style="width: 100%">
              <template #suffix>元/MWh</template>
            </el-input-number>
          </el-form-item>

          <el-form-item label="市场最大成交价格">
            <el-input-number v-model="marketForm.max_clearing_price" :min="0" :precision="2" :step="0.01" style="width: 100%">
              <template #suffix>元/MWh</template>
            </el-input-number>
          </el-form-item>

          <el-form-item label="市场最小申报价格">
            <el-input-number v-model="marketForm.min_bid_price" :min="0" :precision="2" :step="0.01" style="width: 100%">
              <template #suffix>元/MWh</template>
            </el-input-number>
          </el-form-item>

          <el-form-item label="市场最大申报价格">
            <el-input-number v-model="marketForm.max_bid_price" :min="0" :precision="2" :step="0.01" style="width: 100%">
              <template #suffix>元/MWh</template>
            </el-input-number>
          </el-form-item>

          <el-form-item label="备用率">
            <el-input-number v-model="marketForm.reserve_rate" :min="0" :max="100" :precision="1" :step="0.1" style="width: 100%">
              <template #suffix>%</template>
            </el-input-number>
          </el-form-item>

          <el-form-item label="网架">
            <el-select v-model="marketForm.grid" placeholder="请选择网架" style="width: 100%">
              <el-option label="陕西电网" value="shaanxi_grid"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="市场时段数">
            <el-select v-model="marketForm.period_count" placeholder="请选择市场时段数" style="width: 100%">
              <el-option label="24段" value="24"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="是否考虑负荷弹性">
            <el-radio-group v-model="marketForm.consider_load_elasticity">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="是否考虑备用出清价格">
            <el-radio-group v-model="marketForm.consider_reserve_clearing_price">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="负荷曲线">
            <el-upload
              action="#"
              :auto-upload="false"
              :limit="1"
              :on-change="handleLoadCurveChange">
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">请上传负荷曲线文件</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤7: 市场主体信息配置 -->
      <div v-if="activeStep === 7" class="step-container">
        <h3>市场主体信息配置</h3>
        <el-form :model="marketForm" label-width="180px" class="market-form">
          <el-form-item label="目标函数">
            <el-select v-model="marketForm.objective_function" placeholder="请选择目标函数" style="width: 100%">
              <el-option label="购电成本最小" value="min_purchase_cost"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="约束条件">
            <el-checkbox-group v-model="marketForm.constraints">
              <div class="constraint-list">
                <el-checkbox label="power_balance">功率平衡约束</el-checkbox>
                <el-checkbox label="power_limit">功率上下限约束</el-checkbox>
                <el-checkbox label="ramp">爬坡约束</el-checkbox>
                <el-checkbox label="reserve">备用约束</el-checkbox>
                <el-checkbox label="section">断面约束</el-checkbox>
                <el-checkbox label="power_flow">潮流约束</el-checkbox>
                <el-checkbox label="unit_commitment">启停约束</el-checkbox>
              </div>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="机组类型">
            <el-checkbox-group v-model="marketForm.unit_types">
              <div class="unit-type-list">
                <el-checkbox label="thermal">火电厂</el-checkbox>
                <el-checkbox label="hydro">水电厂</el-checkbox>
                <el-checkbox label="wind">风电场</el-checkbox>
                <el-checkbox label="solar">光伏电站</el-checkbox>
              </div>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="允许参与的用户类型">
            <el-select v-model="marketForm.allowed_user_types" multiple placeholder="请选择允许参与的用户类型" style="width: 100%">
              <el-option label="储能用户" value="storage"></el-option>
              <el-option label="火电用户" value="thermal"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="最大参与人数">
            <el-input-number v-model="marketForm.max_participants" :min="1" :precision="0" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="step-actions">
      <el-button v-if="activeStep > 1" @click="prevStep">上一步</el-button>
      <el-button v-if="activeStep < 7" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-if="activeStep === 7" type="success" @click="submitForm">保存配置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createMarket, getMarketDetail, updateMarket, uploadLoadCurve } from '../../api/market'

const router = useRouter()

// 当前步骤
const activeStep = ref(1)

// 是否为编辑模式
const isEditMode = ref(false)
const editMarketId = ref(null)

// 表单数据
const marketForm = reactive({
  // 步骤1: 时间尺度选择
  time_scale: 'day_ahead',

  // 步骤2: 交易品种选择
  product_type: 'energy',

  // 步骤3: 交易类型选择
  trade_type: 'centralized_bidding',

  // 步骤4: 报价方式选择
  bidding_method: 'one_side',

  // 步骤5: 结算方式选择
  settlement_method: 'nodal_marginal_price',

  // 步骤6: 市场整体信息配置
  name: '',
  type: 'day_ahead',
  description: '',
  start_date: '',
  end_date: '',
  min_clearing_price: 0,
  max_clearing_price: 0,
  min_bid_price: 1000,
  max_bid_price: 1000,
  reserve_rate: 5,
  grid: 'shaanxi_grid',
  period_count: '24',
  consider_load_elasticity: false,
  consider_reserve_clearing_price: false,
  load_curve_file: null,

  // 步骤7: 市场主体信息配置
  objective_function: 'min_purchase_cost',
  constraints: ['power_balance', 'power_limit', 'ramp', 'reserve', 'section', 'power_flow', 'unit_commitment'],
  unit_types: ['thermal', 'hydro', 'wind', 'solar'],

  // 参与者配置
  allowed_user_types: ['type1', 'type2'],
  max_participants: 50
})

// 加载市场详情
const loadMarketDetail = async (id) => {
  try {
    const market = await getMarketDetail(id)

    // 更新表单数据
    Object.keys(market).forEach(key => {
      // 跳过只读字段
      if (['id', 'created_at', 'updated_at', 'published_at', 'is_published', 'creator'].includes(key)) {
        return
      }

      // 更新表单数据
      if (key in marketForm) {
        // 特殊处理allowed_user_types字段
        if (key === 'allowed_user_types' && typeof market[key] === 'string') {
          marketForm[key] = market[key].split(',')
        } else {
          marketForm[key] = market[key]
        }
      }
    })

    ElMessage.success('加载市场详情成功')
  } catch (error) {
    console.error('加载市场详情失败:', error)
    ElMessage.error('加载市场详情失败，请重试')
  }
}

// 下一步
const nextStep = () => {
  if (activeStep.value < 7) {
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 1) {
    activeStep.value--
  }
}

// 处理负荷曲线文件变化
const handleLoadCurveChange = (file) => {
  marketForm.load_curve_file = file.raw
}

// 提交表单
const submitForm = async () => {
  try {
    // 表单验证
    if (!marketForm.name) {
      ElMessage.error('请输入市场名称')
      return
    }

    if (!marketForm.start_date) {
      ElMessage.error('请选择开始时间')
      return
    }

    if (!marketForm.end_date) {
      ElMessage.error('请选择结束时间')
      return
    }

    // 准备提交的数据
    const formData = { ...marketForm }

    // 移除文件字段，文件需要单独上传
    const loadCurveFile = formData.load_curve_file
    delete formData.load_curve_file

    // 将数组转换为逗号分隔的字符串
    if (Array.isArray(formData.allowed_user_types)) {
      formData.allowed_user_types = formData.allowed_user_types.join(',')
    }

    // 转换约束条件为对象数组
    if (Array.isArray(formData.constraints)) {
      formData.constraints = formData.constraints.map(constraint => ({ type: constraint }))
    }

    // 转换机组类型为对象数组
    if (Array.isArray(formData.unit_types)) {
      formData.unit_types = formData.unit_types.map(unitType => ({ type: unitType }))
    }

    // 确保日期格式正确
    if (formData.start_date instanceof Date) {
      formData.start_date = formData.start_date.toISOString()
    }

    if (formData.end_date instanceof Date) {
      formData.end_date = formData.end_date.toISOString()
    }

    console.log('提交的数据:', formData)

    let marketId

    if (isEditMode.value) {
      // 更新市场
      const response = await updateMarket(editMarketId.value, formData)
      marketId = response.id
      ElMessage.success('市场更新成功')
    } else {
      // 创建市场
      const response = await createMarket(formData)
      marketId = response.id
      ElMessage.success('市场创建成功')
    }

    // 如果有负荷曲线文件，上传文件
    if (loadCurveFile && marketId) {
      await uploadLoadCurve(marketId, loadCurveFile)
      ElMessage.success('负荷曲线文件上传成功')
    }

    // 跳转到未发布市场页面
    router.push('/market-config/unpublished')
  } catch (error) {
    console.error('提交表单失败:', error)

    // 显示详细错误信息
    if (error.message) {
      ElMessage.error(`提交表单失败: ${error.message}`)
    } else {
      ElMessage.error('提交表单失败，请重试')
    }
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 检查是否为编辑模式
  const storedMarketId = localStorage.getItem('editMarketId')
  if (storedMarketId) {
    isEditMode.value = true
    editMarketId.value = storedMarketId

    // 加载市场详情
    loadMarketDetail(storedMarketId)

    // 清除存储的市场ID
    localStorage.removeItem('editMarketId')
  }
})
</script>

<style scoped>
.market-config-wizard {
  padding: 20px 0;
}

h2 {
  margin-bottom: 20px;
  font-weight: 500;
  color: #303133;
}

h3 {
  margin-bottom: 20px;
  font-weight: 500;
  color: #303133;
}

.config-steps {
  margin-bottom: 30px;
}

.step-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  min-height: 400px;
  margin-bottom: 20px;
}

.step-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-card {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.step-description {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  color: #606266;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.market-form {
  max-width: 800px;
}

.constraint-list, .unit-type-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
