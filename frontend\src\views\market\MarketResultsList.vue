<template>
  <div class="market-results-list">
    <h2>市场结果列表</h2>
    
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索市场名称"
          class="search-input"
          clearable
          @clear="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
        
        <el-select v-model="timeScaleFilter" placeholder="时间尺度" clearable @change="handleSearch">
          <el-option label="日前" value="day_ahead"></el-option>
        </el-select>
        
        <el-select v-model="productTypeFilter" placeholder="交易品种" clearable @change="handleSearch">
          <el-option label="能量" value="energy"></el-option>
        </el-select>
        
        <el-select v-model="tradeTypeFilter" placeholder="交易类型" clearable @change="handleSearch">
          <el-option label="集中竞价" value="centralized_bidding"></el-option>
        </el-select>
      </div>
    </el-card>
    
    <el-table
      :data="filteredMarkets"
      border
      style="width: 100%"
      v-loading="loadingMarkets"
    >
      <el-table-column prop="id" label="ID" width="60"></el-table-column>
      <el-table-column prop="name" label="市场名称" min-width="100"></el-table-column>
      <el-table-column prop="time_scale" label="时间尺度" width="100">
        <template #default="scope">
          <el-tag type="info">{{ getTimeScaleLabel(scope.row.time_scale) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="product_type" label="交易品种" width="100">
        <template #default="scope">
          <el-tag type="success">{{ getProductTypeLabel(scope.row.product_type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="trade_type" label="交易类型" width="100">
        <template #default="scope">
          <el-tag type="warning">{{ getTradeTypeLabel(scope.row.trade_type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="bidding_method" label="报价方式" width="100">
        <template #default="scope">
          <el-tag type="primary">{{ getBiddingMethodLabel(scope.row.bidding_method) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlement_method" label="结算方式" width="160">
        <template #default="scope">
          <el-tag type="danger">{{ getSettlementMethodLabel(scope.row.settlement_method) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="navigateToDetail(scope.row)">市场详情</el-button>
          <el-button size="small" type="success" @click="navigateToOverview(scope.row)">总体概览</el-button>
          
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalMarkets"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getPublishedMarkets } from '../../api/market'

const router = useRouter()

// 市场数据
const markets = ref([])
const loadingMarkets = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalMarkets = ref(0)

// 搜索和筛选
const searchQuery = ref('')
const timeScaleFilter = ref('')
const productTypeFilter = ref('')
const tradeTypeFilter = ref('')

// 过滤后的市场列表
const filteredMarkets = computed(() => {
  let result = markets.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(market => 
      market.name.toLowerCase().includes(query)
    )
  }
  
  // 时间尺度过滤
  if (timeScaleFilter.value) {
    result = result.filter(market => market.time_scale === timeScaleFilter.value)
  }
  
  // 交易品种过滤
  if (productTypeFilter.value) {
    result = result.filter(market => market.product_type === productTypeFilter.value)
  }
  
  // 交易类型过滤
  if (tradeTypeFilter.value) {
    result = result.filter(market => market.trade_type === tradeTypeFilter.value)
  }
  
  // 更新总数
  totalMarkets.value = result.length
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  
  return result.slice(start, end)
})

// 获取时间尺度标签
const getTimeScaleLabel = (scale) => {
  const scaleMap = {
    'day_ahead': '日前'
  }
  return scaleMap[scale] || '未知'
}

// 获取交易品种标签
const getProductTypeLabel = (type) => {
  const typeMap = {
    'energy': '能量'
  }
  return typeMap[type] || '未知'
}

// 获取交易类型标签
const getTradeTypeLabel = (type) => {
  const typeMap = {
    'centralized_bidding': '集中竞价'
  }
  return typeMap[type] || '未知'
}

// 获取报价方式标签
const getBiddingMethodLabel = (method) => {
  const methodMap = {
    'one_side': '单边报价'
  }
  return methodMap[method] || '未知'
}

// 获取结算方式标签
const getSettlementMethodLabel = (method) => {
  const methodMap = {
    'nodal_marginal_price': '节点边际电价出清'
  }
  return methodMap[method] || '未知'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'active': '进行中',
    'ended': '已结束',
    'pending': '未开始',
    'draft': '草稿'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    'active': 'success',
    'ended': 'info',
    'pending': 'warning',
    'draft': 'danger'
  }
  return statusMap[status] || 'info'
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

// 导航到市场详情页面
const navigateToDetail = (market) => {
  router.push(`/market-result-detail/${market.id}`)
}

// 导航到总体概览页面
const navigateToOverview = (market) => {
  router.push(`/market-result-overview/${market.id}`)
}

// 导航到经济效益页面
const navigateToEconomic = (market) => {
  router.push(`/market-result-economic/${market.id}`)
}

// 加载市场数据
const loadMarkets = async () => {
  loadingMarkets.value = true
  
  try {
    // 构建查询参数
    const params = {
      page: 1,
      page_size: 1000,
      status: 'ended'  // 只获取已结束的市场
    }
    
    // 调用API获取已发布市场列表
    const response = await getPublishedMarkets(params)
    
    // 更新数据
    if (Array.isArray(response)) {
      markets.value = response
    } else if (response.results) {
      markets.value = response.results
    } else {
      markets.value = []
      console.error('未知的响应格式:', response)
    }
    
    totalMarkets.value = markets.value.length
    ElMessage.success('市场数据已加载')
  } catch (error) {
    console.error('加载市场数据失败:', error)
    ElMessage.error('加载市场数据失败，请重试')
    markets.value = []
    totalMarkets.value = 0
  } finally {
    loadingMarkets.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadMarkets()
})
</script>

<style scoped>
.market-results-list {
  padding: 20px 0;
}

h2 {
  margin-bottom: 20px;
  font-weight: 500;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-input {
  width: 300px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
