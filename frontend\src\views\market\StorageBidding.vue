<template>
  <div class="storage-bidding">
    <div class="page-header">
      <h2>储能报价</h2>
      <el-button type="primary" @click="goBack">返回</el-button>
    </div>

    <div v-loading="loading">
      <!-- 市场基本信息 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>市场信息</h3>
          </div>
        </template>
        <div class="market-info">
          <div class="info-item">
            <span class="label">市场名称:</span>
            <span class="value">{{ market.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">时间尺度:</span>
            <span class="value">
              <el-tag type="info">{{ getTimeScaleLabel(market.time_scale) }}</el-tag>
            </span>
          </div>
          <div class="info-item">
            <span class="label">交易品种:</span>
            <span class="value">
              <el-tag type="success">{{ getProductTypeLabel(market.product_type) }}</el-tag>
            </span>
          </div>
          <div class="info-item">
            <span class="label">报价方式:</span>
            <span class="value">
              <el-tag type="primary">{{ getBiddingMethodLabel(market.bidding_method) }}</el-tag>
            </span>
          </div>
          <div class="info-item">
            <span class="label">开始时间:</span>
            <span class="value">{{ formatDateTime(market.start_date) }}</span>
          </div>
          <div class="info-item">
            <span class="label">结束时间:</span>
            <span class="value">{{ formatDateTime(market.end_date) }}</span>
          </div>
          <div class="info-item">
            <span class="label">状态:</span>
            <span class="value">
              <el-tag :type="getStatusTag(market.status)">{{ getStatusLabel(market.status) }}</el-tag>
            </span>
          </div>
        </div>
      </el-card>

      <!-- 储能报价表单 -->
      <el-card class="bidding-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>储能报价信息</h3>
            <div class="header-actions" v-if="!hasExistingBidding">

              <el-button type="danger" size="small" @click="clearBiddings">清空报价</el-button>
            </div>
            <div class="header-info" v-if="hasExistingBidding">
              <el-tag type="info">已有报价，只能修改现有数据</el-tag>
            </div>
          </div>
        </template>

        <div class="bidding-form">
          <div class="price-info">
            <p class="price-range">
              申报价格范围: {{ market.min_bid_price || 0 }} - {{ market.max_bid_price || 1000 }} 元/MWh
            </p>
            <p class="storage-note">
              <el-icon><InfoFilled /></el-icon>
              储能用户只需输入充电报价和放电报价
            </p>
          </div>

          <el-table :data="biddings" border style="width: 100%">
          
            <el-table-column label="充电报价 (元/MWh)" width="200">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.charge_price"
                  :min="market.min_bid_price || 0"
                  :max="market.max_bid_price || 1000"
                  :precision="2"
                  :step="0.1"
                  controls-position="right"
                  placeholder="充电报价"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="放电报价 (元/MWh)" width="200">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.discharge_price"
                  :min="market.min_bid_price || 0"
                  :max="market.max_bid_price || 1000"
                  :precision="2"
                  :step="0.1"
                  controls-position="right"
                  placeholder="放电报价"
                ></el-input-number>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" v-if="!hasExistingBidding" width="80">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeBiddingRow(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="form-actions">
            <el-button
              type="primary"
              size="large"
              @click="submitBidding"
              :disabled="!canSubmit"
            >
              {{ hasExistingBidding ? '修改报价' : '提交报价' }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { useUserStore } from '../../store/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 市场数据
const market = ref({})
const loading = ref(false)
const biddings = ref([])
const hasExistingBidding = ref(false)

// 获取时间尺度标签
const getTimeScaleLabel = (scale) => {
  const scaleMap = {
    'day_ahead': '日前'
  }
  return scaleMap[scale] || '未知'
}

// 获取交易品种标签
const getProductTypeLabel = (type) => {
  const typeMap = {
    'energy': '能量'
  }
  return typeMap[type] || '未知'
}

// 获取报价方式标签
const getBiddingMethodLabel = (method) => {
  const methodMap = {
    'one_side': '单边报价'
  }
  return methodMap[method] || '未知'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'active': '进行中',
    'ended': '已结束',
    'pending': '未开始'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    'active': 'success',
    'ended': 'info',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'

  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateTimeStr
  }
}

// 可用的时段
const availablePeriods = computed(() => {
  if (!market.value.period_count) return []

  const count = parseInt(market.value.period_count) || 24
  return Array.from({ length: count }, (_, i) => i + 1)
})

// 判断是否可以提交
const canSubmit = computed(() => {
  return biddings.value.length > 0 &&
         biddings.value.every(b =>
           b.period &&
           b.charge_price >= 0 &&
           b.discharge_price >= 0 &&
           b.charge_price !== null &&
           b.discharge_price !== null
         )
})

// 导入API服务
import { getMarketDetail, submitStorageBidding, getStorageBiddings, joinMarket } from '../../api/storage'

// 加载市场详情
const loadMarketDetail = async () => {
  loading.value = true

  try {
    // 从路由参数或本地存储获取市场ID
    const marketId = route.query.id || localStorage.getItem('biddingMarketId')

    if (!marketId) {
      ElMessage.error('未找到市场ID')
      router.push('/user-markets')
      return
    }

    // 调用API获取市场详情
    const response = await getMarketDetail(marketId)
    market.value = response

    // 尝试加入市场（如果还没有加入的话）
    await tryJoinMarket(marketId)

    // 检查是否已有报价
    await checkExistingBiddings(marketId)

    ElMessage.success('加载市场详情成功')
  } catch (error) {
    console.error('加载市场详情失败:', error)
    ElMessage.error('加载市场详情失败，请重试')
  } finally {
    loading.value = false
  }
}

// 尝试加入市场
const tryJoinMarket = async (marketId) => {
  try {
    await joinMarket(marketId)
    console.log('成功加入市场')
  } catch (error) {
    // 如果已经加入市场，会返回400错误，这是正常的
    if (error.response && error.response.status === 400) {
      console.log('用户已经参与此市场或其他原因:', error.response.data.detail)
    } else {
      console.error('加入市场失败:', error)
      ElMessage.warning('加入市场失败，但可以继续查看市场信息')
    }
  }
}

// 检查是否已有报价
const checkExistingBiddings = async (marketId) => {
  try {
    console.log('正在检查市场ID为', marketId, '的现有报价...')

    // 调用API获取当前用户在当前市场的储能报价
    const existingBiddings = await getStorageBiddings({
      market_id: marketId,
      limit: 100  // 确保获取所有报价
    })

    console.log('API返回的报价数据:', existingBiddings)

    if (existingBiddings && existingBiddings.length > 0) {
      hasExistingBidding.value = true
      console.log('发现现有报价:', existingBiddings.length, '条')

      // 加载现有报价数据，确保数据格式正确
      biddings.value = existingBiddings.map(b => ({
        id: b.id,  // 保存报价ID用于后续更新
        period: b.period,
        charge_price: parseFloat(b.charge_price) || 0,
        discharge_price: parseFloat(b.discharge_price) || 0,
        remarks: b.remarks || ''
      }))

      console.log('加载的报价数据:', biddings.value)
      ElMessage.info(`已加载现有报价 ${existingBiddings.length} 条，只能修改不能新增`)
    } else {
      hasExistingBidding.value = false
      console.log('没有发现现有报价，初始化空表单')

      // 初始化一个空的报价行
      addBiddingRow()
    }
  } catch (error) {
    console.error('检查现有报价失败:', error)

    // 如果是404错误，说明没有报价数据
    if (error.response && error.response.status === 404) {
      hasExistingBidding.value = false
      console.log('没有找到现有报价，初始化空表单')
      addBiddingRow()
    } else {
      // 其他错误，显示错误信息但仍允许用户操作
      ElMessage.warning('获取现有报价失败，但您仍可以提交新报价')
      hasExistingBidding.value = false
      addBiddingRow()
    }
  }
}

// 添加报价行
const addBiddingRow = () => {
  // 如果已有现有报价，不允许添加新行
  if (hasExistingBidding.value) {
    ElMessage.warning('已有报价，不能添加新的时段')
    return
  }

  biddings.value.push({
    period: null,
    charge_price: market.value.min_bid_price || 0,
    discharge_price: market.value.min_bid_price || 0,
    remarks: ''
  })
}

// 移除报价行
const removeBiddingRow = (index) => {
  // 如果已有现有报价，不允许删除行
  if (hasExistingBidding.value) {
    ElMessage.warning('已有报价，不能删除现有时段')
    return
  }

  biddings.value.splice(index, 1)
}

// 清空报价
const clearBiddings = () => {
  // 如果已有现有报价，不允许清空
  if (hasExistingBidding.value) {
    ElMessage.warning('已有报价，不能清空现有数据')
    return
  }

  ElMessageBox.confirm('确定要清空所有报价吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    biddings.value = []
    ElMessage.success('已清空所有报价')
  }).catch(() => {
    // 用户取消操作
  })
}

// 提交报价
const submitBidding = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('请完善报价信息')
    return
  }

  try {
    const confirmText = hasExistingBidding.value ? '确定要修改报价吗?' : '确定要提交报价吗?'
    await ElMessageBox.confirm(confirmText, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    loading.value = true

    // 构建提交数据
    const data = {
      market_id: market.value.id,
      biddings: biddings.value.map(b => ({
        period: b.period,
        charge_price: parseFloat(b.charge_price) || 0,
        discharge_price: parseFloat(b.discharge_price) || 0,
        remarks: b.remarks || ''
      }))
    }

    console.log('提交的报价数据:', data)

    // 调用API提交储能报价
    await submitStorageBidding(data)

    const successText = hasExistingBidding.value ? '报价修改成功' : '报价提交成功'
    ElMessage.success(successText)
    router.push('/user-markets')
  } catch (error) {
    if (error.response) {
      ElMessage.error(`提交失败: ${error.response.data.detail || '未知错误'}`)
    } else if (error.message) {
      // 用户取消操作
      console.log('取消提交')
    } else {
      ElMessage.error('提交失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 组件挂载时加载数据
onMounted(() => {
  // 检查用户类型
  if (userStore.user.user_type !== 'storage') {
    ElMessage.error('只有储能用户可以访问此页面')
    router.push('/user-markets')
    return
  }

  loadMarketDetail()
})
</script>

<style scoped>
.storage-bidding {
  padding: 20px 0;
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h2 {
  margin: 0;
  font-weight: 500;
  color: #303133;
}

.info-card, .bidding-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h3 {
  margin: 0;
  font-weight: 500;
  color: #303133;
}

.market-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
  min-width: 100px;
}

.value {
  color: #303133;
}

.bidding-form {
  margin-top: 20px;
}

.price-info {
  margin-bottom: 20px;
}

.price-range {
  margin-bottom: 10px;
  color: #606266;
  font-weight: bold;
}

.storage-note {
  margin: 0;
  color: #409EFF;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-info {
  display: flex;
  align-items: center;
}
</style>
