<template>
  <div class="market-result-overview">
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <span>总体概览 - {{ market ? market.name : '加载中...' }}</span>
          <el-button type="primary" size="small" @click="goBack">返回</el-button>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else-if="market" class="overview-content">
        <!-- 市场基本信息 -->
        <el-descriptions title="市场基本信息" :column="3" border>
          <el-descriptions-item label="市场ID">{{ market.id }}</el-descriptions-item>
          <el-descriptions-item label="市场名称">{{ market.name }}</el-descriptions-item>
          <el-descriptions-item label="时间尺度">{{ getTimeScaleLabel(market.time_scale) }}</el-descriptions-item>
          <el-descriptions-item label="交易品种">{{ getProductTypeLabel(market.product_type) }}</el-descriptions-item>
          <el-descriptions-item label="交易类型">{{ getTradeTypeLabel(market.trade_type) }}</el-descriptions-item>
          <el-descriptions-item label="报价方式">{{ getBiddingMethodLabel(market.bidding_method) }}</el-descriptions-item>
          <el-descriptions-item label="结算方式">{{ getSettlementMethodLabel(market.settlement_method) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTag(market.status)">{{ getStatusLabel(market.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始日期">{{ formatDateTime(market.start_date) }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 总体交易看板 -->
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>总体交易看板</span>
            </div>
          </template>
          
          <el-table
            :data="tradeDashboard"
            border
            style="width: 100%"
          >
            <el-table-column prop="marketName" label="市场名称" min-width="150"></el-table-column>
            <el-table-column prop="totalVolume" label="总交易电量 (MWh)" width="150"></el-table-column>
            <el-table-column prop="avgPrice" label="平均交易电价 (元/kWh)" width="180"></el-table-column>
            <el-table-column prop="totalAmount" label="总交易金额 (万元)" width="150"></el-table-column>
            <el-table-column prop="participantCount" label="参与主体数量" width="120"></el-table-column>
            <el-table-column prop="transactionCount" label="成交笔数" width="100"></el-table-column>
          </el-table>
        </el-card>
        
        <!-- 市场主体交易情况 -->
        <el-card class="participants-card">
          <template #header>
            <div class="card-header">
              <span>市场主体交易情况</span>
              <div class="filter-container">
                <el-select v-model="participantTypeFilter" placeholder="主体类型" clearable @change="filterParticipants">
                  <el-option label="发电方" value="generator"></el-option>
                  <el-option label="用电方" value="consumer"></el-option>
                  <el-option label="储能方" value="storage"></el-option>
                </el-select>
              </div>
            </div>
          </template>
          
          <el-table
            :data="filteredParticipants"
            border
            style="width: 100%"
            v-loading="loadingParticipants"
          >
            <el-table-column prop="id" label="ID" width="60"></el-table-column>
            <el-table-column prop="name" label="主体名称" min-width="150"></el-table-column>
            <el-table-column prop="type" label="主体类型" width="100">
              <template #default="scope">
                <el-tag :type="getParticipantTypeTag(scope.row.type)">
                  {{ getParticipantTypeLabel(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="volume" label="交易电量 (MWh)" width="150"></el-table-column>
            <el-table-column prop="avgPrice" label="平均价格 (元/kWh)" width="150"></el-table-column>
            <el-table-column prop="amount" label="交易金额 (万元)" width="150"></el-table-column>
            <el-table-column prop="transactionCount" label="成交笔数" width="100"></el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="scope">
                <el-button size="small" type="primary" @click="viewParticipantDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalParticipants"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      
      
      </div>
      
      <div v-else class="error-container">
        <el-empty description="无法加载市场数据"></el-empty>
      </div>
    </el-card>
    
    <!-- 主体详情对话框 -->
    <el-dialog
      v-model="participantDetailVisible"
      :title="selectedParticipant ? `${selectedParticipant.name} 交易详情` : '交易详情'"
      width="70%"
    >
      <div v-if="selectedParticipant" class="participant-detail">
        <el-descriptions title="主体信息" :column="3" border>
          <el-descriptions-item label="主体ID">{{ selectedParticipant.id }}</el-descriptions-item>
          <el-descriptions-item label="主体名称">{{ selectedParticipant.name }}</el-descriptions-item>
          <el-descriptions-item label="主体类型">
            <el-tag :type="getParticipantTypeTag(selectedParticipant.type)">
              {{ getParticipantTypeLabel(selectedParticipant.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="交易电量">{{ selectedParticipant.volume }} MWh</el-descriptions-item>
          <el-descriptions-item label="平均价格">{{ selectedParticipant.avgPrice }} 元/kWh</el-descriptions-item>
          <el-descriptions-item label="交易金额">{{ selectedParticipant.amount }} 万元</el-descriptions-item>
        </el-descriptions>
        
        <h4>交易明细</h4>
        <el-table
          :data="participantTransactions"
          border
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column prop="id" label="交易ID" width="80"></el-table-column>
          <el-table-column prop="time" label="交易时间" width="180"></el-table-column>
          <el-table-column prop="counterpartyName" label="交易对手" min-width="150"></el-table-column>
          <el-table-column prop="counterpartyType" label="对手类型" width="100">
            <template #default="scope">
              <el-tag :type="getParticipantTypeTag(scope.row.counterpartyType)">
                {{ getParticipantTypeLabel(scope.row.counterpartyType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="时段" width="80"></el-table-column>
          <el-table-column prop="price" label="价格 (元/kWh)" width="120"></el-table-column>
          <el-table-column prop="volume" label="电量 (MWh)" width="120"></el-table-column>
          <el-table-column prop="amount" label="金额 (万元)" width="120"></el-table-column>
        </el-table>
        
        <h4>交易趋势</h4>
        <div ref="participantTrendChart" class="detail-chart"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getMarketDetail } from '../../api/market'
import * as echarts from 'echarts/core'
import { PieChart, BarChart, LineChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  TitleComponent, TooltipComponent, LegendComponent, GridComponent,
  PieChart, BarChart, LineChart, CanvasRenderer
])

const route = useRoute()
const router = useRouter()
const market = ref(null)
const loading = ref(true)
const loadingParticipants = ref(false)

// 图表引用
const volumeDistributionChart = ref(null)
const priceDistributionChart = ref(null)
const periodAnalysisChart = ref(null)
const participantTrendChart = ref(null)

// 图表实例
let volumeDistributionChartInstance = null
let priceDistributionChartInstance = null
let periodAnalysisChartInstance = null
let participantTrendChartInstance = null

// 总体交易看板数据
const tradeDashboard = ref([
  {
    marketName: '日前电力市场2023-A',
    totalVolume: '15,680',
    avgPrice: '0.518',
    totalAmount: '812.42',
    participantCount: 45,
    transactionCount: 128
  }
])

// 市场主体数据
const participants = ref([
  {
    id: 101,
    name: '发电公司A',
    type: 'generator',
    volume: '5,200',
    avgPrice: '0.52',
    amount: '270.40',
    transactionCount: 42
  },
  {
    id: 102,
    name: '发电公司B',
    type: 'generator',
    volume: '3,800',
    avgPrice: '0.53',
    amount: '201.40',
    transactionCount: 35
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalParticipants = ref(0)

// 筛选相关
const participantTypeFilter = ref('')

// 主体详情相关
const participantDetailVisible = ref(false)
const selectedParticipant = ref(null)
const participantTransactions = ref([])

// 过滤后的主体列表
const filteredParticipants = computed(() => {
  let result = participants.value
  
  // 主体类型过滤
  if (participantTypeFilter.value) {
    result = result.filter(p => p.type === participantTypeFilter.value)
  }
  
  // 更新总数
  totalParticipants.value = result.length
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  
  return result.slice(start, end)
})

// 获取时间尺度标签
const getTimeScaleLabel = (scale) => {
  const scaleMap = {
    'day_ahead': '日前'
  }
  return scaleMap[scale] || '未知'
}

// 获取交易品种标签
const getProductTypeLabel = (type) => {
  const typeMap = {
    'energy': '能量'
  }
  return typeMap[type] || '未知'
}

// 获取交易类型标签
const getTradeTypeLabel = (type) => {
  const typeMap = {
    'centralized_bidding': '集中竞价'
  }
  return typeMap[type] || '未知'
}

// 获取报价方式标签
const getBiddingMethodLabel = (method) => {
  const methodMap = {
    'one_side': '单边报价'
  }
  return methodMap[method] || '未知'
}

// 获取结算方式标签
const getSettlementMethodLabel = (method) => {
  const methodMap = {
    'nodal_marginal_price': '节点边际电价出清'
  }
  return methodMap[method] || '未知'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'active': '进行中',
    'ended': '已结束',
    'pending': '未开始',
    'draft': '草稿'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    'active': 'success',
    'ended': 'info',
    'pending': 'warning',
    'draft': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取主体类型标签
const getParticipantTypeLabel = (type) => {
  const typeMap = {
    'generator': '发电方',
  }
  return typeMap[type] || '未知类型'
}

// 获取主体类型标签样式
const getParticipantTypeTag = (type) => {
  const typeMap = {
    'generator': 'success',
    'consumer': 'primary',
    'storage': 'warning'
  }
  return typeMap[type] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'

  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateTimeStr
  }
}

// 返回上一页
const goBack = () => {
  router.push('/market-results')
}

// 筛选主体
const filterParticipants = () => {
  currentPage.value = 1
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

// 查看主体详情
const viewParticipantDetail = (participant) => {
  selectedParticipant.value = participant
  participantDetailVisible.value = true

  
  // 初始化趋势图表
  setTimeout(() => {
    initParticipantTrendChart()
  }, 0)
}

// 初始化交易量分布图
const initVolumeDistributionChart = () => {
  if (volumeDistributionChartInstance) {
    volumeDistributionChartInstance.dispose()
  }
  
  volumeDistributionChartInstance = echarts.init(volumeDistributionChart.value)
  
  const option = {
    title: {
      text: '交易电量分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} MWh ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['发电方', '用电方', '储能方']
    },
    series: [
      {
        name: '交易电量',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 9000, name: '发电方' },
          { value: 7700, name: '用电方' },
          { value: 1200, name: '储能方' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  volumeDistributionChartInstance.setOption(option)
}

// 初始化价格分布图
const initPriceDistributionChart = () => {
  if (priceDistributionChartInstance) {
    priceDistributionChartInstance.dispose()
  }
  
  priceDistributionChartInstance = echarts.init(priceDistributionChart.value)
  
  const option = {
    title: {
      text: '交易价格分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0.45-0.48', '0.48-0.51', '0.51-0.54', '0.54-0.57', '0.57-0.60', '0.60以上']
    },
    yAxis: {
      type: 'value',
      name: '交易笔数'
    },
    series: [
      {
        name: '交易笔数',
        type: 'bar',
        data: [15, 30, 45, 25, 10, 3]
      }
    ]
  }
  
  priceDistributionChartInstance.setOption(option)
}

// 初始化时段分析图
const initPeriodAnalysisChart = () => {
  if (periodAnalysisChartInstance) {
    periodAnalysisChartInstance.dispose()
  }
  
  periodAnalysisChartInstance = echarts.init(periodAnalysisChart.value)
  
  const option = {
    title: {
      text: '各时段交易情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['交易电量', '平均价格'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: [
      {
        type: 'value',
        name: '交易电量 (MWh)'
      },
      {
        type: 'value',
        name: '价格 (元/kWh)',
        min: 0.4,
        max: 0.7,
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '交易电量',
        type: 'bar',
        data: [2000, 1800, 3200, 3500, 3000, 2200]
      },
      {
        name: '平均价格',
        type: 'line',
        yAxisIndex: 1,
        data: [0.48, 0.45, 0.55, 0.58, 0.56, 0.52]
      }
    ]
  }
  
  periodAnalysisChartInstance.setOption(option)
}

// 初始化主体趋势图
const initParticipantTrendChart = () => {
  if (!participantTrendChart.value) return
  
  if (participantTrendChartInstance) {
    participantTrendChartInstance.dispose()
  }
  
  participantTrendChartInstance = echarts.init(participantTrendChart.value)
  
  const option = {
    title: {
      text: '交易趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['交易电量', '交易价格'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '交易电量 (MWh)'
      },
      {
        type: 'value',
        name: '价格 (元/kWh)',
        min: 0.4,
        max: 0.7,
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '交易电量',
        type: 'line',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: [400, 350, 650, 700, 600, 450]
      },
      {
        name: '交易价格',
        type: 'line',
        yAxisIndex: 1,
        emphasis: {
          focus: 'series'
        },
        data: [0.48, 0.45, 0.55, 0.58, 0.56, 0.52]
      }
    ]
  }
  
  participantTrendChartInstance.setOption(option)
}

// 加载市场数据
const loadMarketData = async () => {
  loading.value = true
  
  try {
    const marketId = route.params.id
    if (!marketId) {
      throw new Error('未提供市场ID')
    }
    
    const response = await getMarketDetail(marketId)
    market.value = response
    
    // 初始化图表
    setTimeout(() => {
      initVolumeDistributionChart()
      initPriceDistributionChart()
      initPeriodAnalysisChart()
    }, 0)
  } catch (error) {
    console.error('加载市场数据失败:', error)
    ElMessage.error('加载市场数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理窗口大小变化
const handleResize = () => {
  volumeDistributionChartInstance?.resize()
  priceDistributionChartInstance?.resize()
  periodAnalysisChartInstance?.resize()
  participantTrendChartInstance?.resize()
}

onMounted(() => {
  loadMarketData()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  volumeDistributionChartInstance?.dispose()
  priceDistributionChartInstance?.dispose()
  periodAnalysisChartInstance?.dispose()
  participantTrendChartInstance?.dispose()
})
</script>

<style scoped>
.market-result-overview {
  padding: 20px 0;
}

.overview-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.error-container {
  padding: 40px 0;
}

.overview-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dashboard-card, .participants-card, .chart-card {
  margin-top: 20px;
}

.filter-container {
  display: flex;
  gap: 15px;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.chart-container {
  padding: 10px;
}

.chart-row {
  display: flex;
  gap: 20px;
}

.chart {
  height: 300px;
  flex: 1;
}

.participant-detail {
  padding: 10px;
}

.detail-chart {
  height: 300px;
  margin-top: 20px;
}

h4 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-weight: 500;
  color: #303133;
}
</style>
